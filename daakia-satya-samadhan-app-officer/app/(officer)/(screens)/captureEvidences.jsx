import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  ActivityIndicator,
  Alert,
  BackHandler,
  TouchableOpacity,
  Linking,
  Platform
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import * as Location from 'expo-location';
import CapturePageHeader from '../../../components/Smalls/CapturePageHeader';
import CaptureButton from '../../../components/Smalls/CaptureButton';
import OptionsOverlay from '../../../components/Smalls/OptionsOverlay';
import MediaGrid from '../../../components/Smalls/MediaGrid';
import FormSection from '../../../components/Smalls/FormSection';
import PhotoCapture from '../../../components/Larges/PhotoCapture';
import VideoRecordingComp from '../../../components/Larges/VideoRecordingComp';
import SelectField from '../../../components/Smalls/SelectField';
import TitleField from '../../../components/Smalls/TitleField';
import LocationPermissionWarning from '../../../components/Smalls/LocationPermissionWarning';
import { useLocalSearchParams, useRouter } from 'expo-router';
import useUploadMedia from '../../../hooks/useUploadMedia';
import Constants from 'expo-constants';
import { useAuth } from '../../../context/auth-context';
import SuccessScreen from '../../../components/Smalls/SuccessScreen';
import * as IntentLauncher from 'expo-intent-launcher';
import { Colors } from '../../../constants/colors';
import { Ionicons } from '@expo/vector-icons';
import { apiService } from '../../../services/api';

const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

const CaptureEvidences = () => {
  const { caseid } = useLocalSearchParams();
  const router = useRouter();
  const [showOptions, setShowOptions] = useState(false);
  const [media, setMedia] = useState([]);
  const [cameraVisible, setCameraVisible] = useState(false);
  const [videoVisible, setVideoVisible] = useState(false);
  const [description, setDescription] = useState('');
  const [title, setTitle] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [evidenceType, setEvidenceType] = useState('Physical Evidence');
  const [location, setLocation] = useState(null);
  const [locationError, setLocationError] = useState(null);
  const [evidenceQr, setEvidenceQr] = useState(null);
  const [isSuccessVisible, setIsSuccessVisible] = useState(false);
  const { token } = useAuth();
  const [locationEnabled, setLocationEnabled] = useState(false);
  const [selectedTags, setSelectedTags] = useState([]);
  const [tagsOptions, setTagsOptions] = useState([]);
  const [isLoadingTags, setIsLoadingTags] = useState(true);
  const [tagIdMap, setTagIdMap] = useState({});

  const { uploadMedia, error: uploadError } = useUploadMedia();

  // Evidence type options for the dropdown
  const evidenceTypeOptions = [
    { key: 'Physical Evidence', value: 'Physical Evidence' },
    { key: 'Digital Evidence', value: 'Digital Evidence' },
    { key: 'Documentary Evidence', value: 'Documentary Evidence' },
    { key: 'Testimonial Evidence', value: 'Testimonial Evidence' },
  ];

  // Fetch evidence tags from API
  useEffect(() => {
    const fetchTags = async () => {
      try {
        const response = await apiService.fetchEvidenceTags();
        if (response.status === 'success' && response.data) {
          // Create a map of tag types to their IDs
          const idMap = {};
          response.data.forEach(tag => {
            idMap[tag.type] = tag.id;
          });
          setTagIdMap(idMap);

          const formattedTags = response.data.map(tag => ({
            key: tag.type,
            value: tag.type.split('_').map(word => 
              word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ')
          }));
          setTagsOptions(formattedTags);
        }
      } catch (error) {
        console.error('Error fetching evidence tags:', error);
        Alert.alert('Error', 'Failed to load evidence tags');
      } finally {
        setIsLoadingTags(false);
      }
    };

    fetchTags();
  }, []);

  useEffect(() => {
    const backAction = () => {
      if (cameraVisible) {
        setCameraVisible(false);
        return true;
      }
      if (videoVisible) {
        setVideoVisible(false);
        return true;
      }
      return false;
    };
  
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction
    );
  
    return () => backHandler.remove();
  }, [cameraVisible, videoVisible]);

  const openLocationSettings = async () => {
    if (Platform.OS === 'ios') {
      Linking.openSettings();
    } else {
      // For Android
      IntentLauncher.startActivityAsync(
        IntentLauncher.ActivityAction.LOCATION_SOURCE_SETTINGS
      );
    }
  };

  const checkLocationPermissions = async () => {
    try {
      const serviceEnabled = await Location.hasServicesEnabledAsync();
      if (!serviceEnabled) {
        setLocationEnabled(false);
        return;
      }

      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        setLocationEnabled(true);
        // Get location after permissions are granted
        let location = await Location.getCurrentPositionAsync({});
        setLocation(location.coords);
      } else {
        setLocationEnabled(false);
      }
    } catch (error) {
      console.error('Error checking location permissions:', error);
      setLocationEnabled(false);
    }
  };

  useEffect(() => {
    checkLocationPermissions();
  }, []);

  const handlePermissionGranted = useCallback(() => {
    setLocationEnabled(true);
    // Get location after permissions are granted
    Location.getCurrentPositionAsync({})
      .then(location => {
        setLocation(location.coords);
      })
      .catch(error => {
        console.error('Error getting location:', error);
      });
  }, []);

  const handleCaptureOption = (option) => {
    setShowOptions(false); 
    if (option === 'photo') {
      setCameraVisible(true);
    } else if (option === 'video') {
      setVideoVisible(true);
    }
  };

  const handleDeleteMedia = (index) => {
    setMedia((prevMedia) => prevMedia.filter((_, i) => i !== index));
  };

  const handlePhotoCaptured = (uri) => {
    const mediaId = Date.now().toString();
    setMedia((prevMedia) => [
      ...prevMedia,
      { id: mediaId, uri, type: 'image', uploaded: false }
    ]);
    setCameraVisible(false);
  };

  const handleVideoCaptured = (uri) => {
    const mediaId = Date.now().toString();
    setMedia((prevMedia) => [
      ...prevMedia,
      { id: mediaId, uri, type: 'video', uploaded: false }
    ]);
    setVideoVisible(false);
  };

  const handleBackPressed = () => {
    setVideoVisible(false);
  };

  const handleRecordingComplete = () => {
    setVideoVisible(false);
  };

  const handleSubmit = async () => {
    // Validation checks
    if (!caseid) {
      Alert.alert('Error', 'No case ID provided');
      return;
    }

    if (media.length === 0) {
      Alert.alert('Error', 'No media selected');
      return;
    }

    if (!title.trim()) {
      Alert.alert('Error', 'Please provide a title');
      return;
    }

    if (!description.trim()) {
      Alert.alert('Error', 'Please provide a description');
      return;
    }

    setIsUploading(true);
    setIsSaving(true);
    setUploadProgress(0);

    try {
      // Step 1: Upload all media files in parallel
      const uploadPromises = media.map((item, index) => {
        return uploadMedia(item.uri, item.type)
          .then(url => {
            // Update the media item status
            setMedia(prevMedia => 
              prevMedia.map(mediaItem => 
                mediaItem.id === item.id 
                  ? { ...mediaItem, uploaded: true, url } 
                  : mediaItem
              )
            );
            // Update progress
            setUploadProgress(prev => prev + (1 / media.length * 100));
            return url;
          })
          .catch(err => {
            console.error(`Failed to upload ${item.type}:`, err);
            return null; // Return null for failed uploads
          });
      });

      const uploadResults = await Promise.all(uploadPromises);
      
      // Step 2: Check for upload failures
      const successfulUploads = uploadResults.filter(Boolean);
      const failedUploads = uploadResults.length - successfulUploads.length;
      
      if (failedUploads > 0) {
        // Some uploads failed - ask user if they want to continue
        Alert.alert(
          'Upload Warning',
          `${failedUploads} out of ${uploadResults.length} uploads failed. Do you want to continue with the successfully uploaded media?`,
          [
            { 
              text: 'Cancel', 
              style: 'cancel', 
              onPress: () => {
                setIsSaving(false);
                setIsUploading(false);
                setUploadProgress(0);
              } 
            },
            { 
              text: 'Continue', 
              onPress: () => completeSubmission(successfulUploads) 
            }
          ]
        );
      } else if (successfulUploads.length > 0) {
        // All uploads succeeded
        await completeSubmission(successfulUploads);
      } else {
        // All uploads failed
        Alert.alert('Error', 'All media uploads failed. Please try again.');
        setIsSaving(false);
        setIsUploading(false);
        setUploadProgress(0);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed during upload process: ' + (error.message || 'Unknown error'));
      setIsSaving(false);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // Helper function to complete the submission
  const completeSubmission = async (uploadedUrls) => {
    try {
      // Get tag IDs for selected tags
      const selectedTagIds = selectedTags.map(tagType => tagIdMap[tagType]);

      // Prepare payload with location
      const payload = {
        type: evidenceType,
        title: title.trim(),
        description: description.trim(),
        gpsLocation: location
          ? `Latitude: ${location.latitude}, Longitude: ${location.longitude}`
          : 'Location not available',
        attachmentUrl: uploadedUrls,
        tags: selectedTagIds, // Add the tag IDs to the payload
      };

      console.log('Sending payload:', JSON.stringify(payload, null, 2));

      const result = await apiService.submitEvidence(token, caseid, payload);
      // console.log('Response from server:', JSON.stringify(result, null, 2));

      // Store QR code if available
      if (result.data && result.data.qrCode) {
        setEvidenceQr(result.data.qrCode);
      }

      setIsSuccessVisible(true);
    } catch (error) {
      console.error('Error details:', error);
      Alert.alert('Error', error.message || 'Failed to save evidence');
      console.error('Error saving evidence:', error);
    } finally {
      setIsSaving(false);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <ScrollView style={styles.content} contentContainerStyle={{ paddingBottom: 20 }}>
          <CapturePageHeader
            title="Capture Evidences*"
            subtitle="Collect and upload all relevant evidence for this case. Ensure each item is accurately documented and securely stored to maintain the integrity of the investigation."
          />

          {!locationEnabled && (
            <LocationPermissionWarning onPermissionGranted={handlePermissionGranted} />
          )}

          <CaptureButton 
            onPress={() => locationEnabled ? setShowOptions(true) : checkLocationPermissions()} 
          />

          {showOptions && (
            <OptionsOverlay
              visible={showOptions} 
              onSelectOption={handleCaptureOption} 
              onClose={() => setShowOptions(false)} 
            />
          )}

          {media.length > 0 && (
            <>
              <MediaGrid
                media={media}
                onDeleteMedia={handleDeleteMedia}
                thumbnailSize={150}
              />
              
              {uploadError && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>Error: {uploadError}</Text>
                </View>
              )}

              {isUploading && (
                <View style={styles.uploadSummary}>
                  <Text style={styles.uploadTitle}>
                    Uploading Media... ({Math.round(uploadProgress)}%)
                  </Text>
                  <View style={styles.progressBarContainer}>
                    <View 
                      style={[
                        styles.progressBar, 
                        { width: `${uploadProgress}%` }
                      ]} 
                    />
                  </View>
                  <View style={styles.uploadingIndicator}>
                    <ActivityIndicator size="small" color="#0066cc" />
                    <Text style={styles.uploadingText}>
                      Uploading {media.length} files...
                    </Text>
                  </View>
                </View>
              )}
            </>
          )}

          <SelectField
            label="Evidence Type"
            data={evidenceTypeOptions}
            onSelect={(selectedType) => setEvidenceType(selectedType)}
            placeholder="Select evidence type"
            searchPlaceholder="Search evidence type"
            selectedValue={evidenceType}
            paddingHorizontal={20}
          />

          <SelectField
            label="Tags"
            data={tagsOptions}
            onSelect={(selectedTag) => setSelectedTags(prev => [...prev, selectedTag])}
            placeholder={isLoadingTags ? "Loading tags..." : "Select a tag"}
            searchPlaceholder="Search tags"
            selectedValue={selectedTags}
            paddingHorizontal={20}
            disabled={isLoadingTags}
            multiple={true}
          />

          <TitleField
            title={title}
            onTitleChange={setTitle}
            paddingHorizontal={20}
          />

          <FormSection
            description={description}
            onDescriptionChange={setDescription}
            onSubmit={handleSubmit}
            isSaving={isSaving}
            buttonText={isSaving ? "Processing..." : "Save Evidence"}
            paddingHorizontal={20}
          />
        </ScrollView>

        {cameraVisible && (
          <View style={styles.cameraOverlay}>
            <PhotoCapture
              setUri={handlePhotoCaptured}
              backPressed={() => setCameraVisible(false)}
            />
          </View>
        )}

        {videoVisible && (
          <View style={styles.cameraOverlay}>
            <VideoRecordingComp
              setUri={handleVideoCaptured}
              backPressed={handleBackPressed}
              onRecordingComplete={handleRecordingComplete}
            />
          </View>
        )}

        {/* Success Screen */}
        {isSuccessVisible && (
          <SuccessScreen
            message="Evidence data synced successfully!"
            duration={2000}
            onComplete={() => {
              setIsSuccessVisible(false);
              router.replace({
                pathname: '(screens)/qrPage',
                params: { qrCode: evidenceQr, caseid: caseid }, 
              });
            }}
          />
        )}
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
  },
  cameraOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',
  },
  errorContainer: {
    padding: 10,
    marginHorizontal: 10,
    backgroundColor: '#ffecec',
    borderRadius: 5,
    marginTop: 10,
  },
  errorText: {
    color: '#ff0000',
  },
  uploadSummary: {
    padding: 10,
    marginHorizontal: 10,
    backgroundColor: '#f0f8ff',
    borderRadius: 5,
    marginTop: 10,
  },
  uploadTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  uploadingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  uploadingText: {
    marginLeft: 10,
    color: '#c4c4c4c',
  },
  progressBarContainer: {
    height: 10,
    backgroundColor: '#e0e0e0',
    borderRadius: 5,
    marginVertical: 5,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#0B36A1',
  },
  locationWarningContainer: {
    backgroundColor: '#FEE2E2',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 10,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#DC2626',
  },
  locationWarningTitle: {
    fontFamily: 'Roboto_bold',
    fontSize: 18,
    color: Colors.black,
    marginBottom: 8,
  },
  locationWarningText: {
    fontFamily: 'Roboto',
    fontSize: 14,
    color: Colors.lightText,
    marginBottom: 16,
    lineHeight: 20,
  },
  locationCheckButton: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  locationCheckButtonText: {
    fontFamily: 'Roboto_bold',
    color: Colors.background,
    fontSize: 16,
    marginLeft: 8,
  },
});

export default CaptureEvidences;